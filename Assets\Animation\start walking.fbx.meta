fileFormatVersion: 2
guid: 700616a3508aba84e89a98c2df880340
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable: []
  externalObjects: {}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: mixamo.com
      takeName: mixamo.com
      internalID: -203655887218126122
      firstFrame: 0
      lastFrame: 44
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: mixamorig:Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: start walking(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Hips
      parentName: start walking(Clone)
      position: {x: 0.0027369028, y: 0.969248, z: 0.0003946632}
      rotation: {x: -0.10809913, y: -0.021372715, z: -0.0071924813, w: 0.9938844}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine
      parentName: mixamorig:Hips
      position: {x: -0, y: 0.102468, z: -0.011828999}
      rotation: {x: 0.012282137, y: 0.00014649189, z: 0.0019839231, w: 0.99992263}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine1
      parentName: mixamorig:Spine
      position: {x: -0, y: 0.120340005, z: 0}
      rotation: {x: 0.1427491, y: -0.00060301024, z: 0.0065738605, w: 0.9897369}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine2
      parentName: mixamorig:Spine1
      position: {x: -0, y: 0.137531, z: 0}
      rotation: {x: 0.1427531, y: 0.000021058791, z: 0.006513923, w: 0.9897369}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Neck
      parentName: mixamorig:Spine2
      position: {x: -0, y: 0.15472299, z: 0}
      rotation: {x: -0.1155523, y: -0.0667485, z: -0.0102534685, w: 0.99100316}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Head
      parentName: mixamorig:Neck
      position: {x: -0, y: 0.061216995, z: 0.021176}
      rotation: {x: -0.040135305, y: -0.19219722, z: -0.09014375, w: 0.9763829}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:HeadTop_End
      parentName: mixamorig:Head
      position: {x: -0, y: 0.234801, z: 0.081221}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftShoulder
      parentName: mixamorig:Spine2
      position: {x: -0.065841, y: 0.13677399, z: -0.0019939998}
      rotation: {x: 0.39672616, y: -0.22681285, z: 0.647976, w: 0.60933685}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftArm
      parentName: mixamorig:LeftShoulder
      position: {x: -0, y: 0.137822, z: 0}
      rotation: {x: -0.10513041, y: 0.30460304, z: -0.041603737, w: 0.945745}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftForeArm
      parentName: mixamorig:LeftArm
      position: {x: -0.000833, y: 0.258616, z: 0.000714}
      rotation: {x: -0.013156059, y: 0.012052442, z: -0.0138256615, w: 0.99974525}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHand
      parentName: mixamorig:LeftForeArm
      position: {x: -0.00068099995, y: 0.240662, z: 0.00015499999}
      rotation: {x: -0.02978976, y: 0.842814, z: 0.06380878, w: 0.53357816}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb1
      parentName: mixamorig:LeftHand
      position: {x: 0.029326, y: 0.028935999, z: 0.012201999}
      rotation: {x: 0.12123593, y: -0.18931526, z: 0.34037456, w: 0.91302073}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb2
      parentName: mixamorig:LeftHandThumb1
      position: {x: 0.0066109993, y: 0.033733, z: 0}
      rotation: {x: 0.0035796969, y: -0.16486788, z: -0.07373478, w: 0.9835492}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb3
      parentName: mixamorig:LeftHandThumb2
      position: {x: -0.00101, y: 0.033165, z: 0}
      rotation: {x: -0.011513428, y: -0.014377928, z: -0.12038677, w: 0.99255615}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb4
      parentName: mixamorig:LeftHandThumb3
      position: {x: -0.005601, y: 0.027857, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex1
      parentName: mixamorig:LeftHand
      position: {x: 0.036817, y: 0.10090499, z: 0.000456}
      rotation: {x: 0.056776658, y: 0.010414299, z: -0.044937395, w: 0.9973208}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex2
      parentName: mixamorig:LeftHandIndex1
      position: {x: -0.00016499999, y: 0.03184, z: 0}
      rotation: {x: 0.017209196, y: -0.000035554145, z: 0.0063416837, w: 0.9998318}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex3
      parentName: mixamorig:LeftHandIndex2
      position: {x: 0.000098, y: 0.028874999, z: 0}
      rotation: {x: 0.032667078, y: -0.0001141583, z: 0.0035558923, w: 0.9994599}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex4
      parentName: mixamorig:LeftHandIndex3
      position: {x: 0.00006699999, y: 0.024758, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle1
      parentName: mixamorig:LeftHand
      position: {x: 0.010221, y: 0.10056599, z: -0.0029159999}
      rotation: {x: 0.050363366, y: 0.01895886, z: -0.046104006, w: 0.9974861}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle2
      parentName: mixamorig:LeftHandMiddle1
      position: {x: -0.00020099999, y: 0.034785, z: 0}
      rotation: {x: 0.015081981, y: -0.00009385677, z: 0.006075344, w: 0.9998678}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle3
      parentName: mixamorig:LeftHandMiddle2
      position: {x: 0.000086, y: 0.031967998, z: 0}
      rotation: {x: 0.035481773, y: -0.000077887555, z: 0.0050098784, w: 0.99935776}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle4
      parentName: mixamorig:LeftHandMiddle3
      position: {x: 0.000114999995, y: 0.028381, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing1
      parentName: mixamorig:LeftHand
      position: {x: -0.012331999, y: 0.099364005, z: -0.003229}
      rotation: {x: 0.04763867, y: 0.035527404, z: -0.04422553, w: 0.9972525}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing2
      parentName: mixamorig:LeftHandRing1
      position: {x: 0.000016, y: 0.029126, z: 0}
      rotation: {x: 0.012026357, y: -0.0000058747805, z: 0.0014466183, w: 0.9999266}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing3
      parentName: mixamorig:LeftHandRing2
      position: {x: 0.0000050000003, y: 0.027437998, z: 0}
      rotation: {x: 0.03187808, y: 0.000014889436, z: 0.0037990224, w: 0.99948454}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing4
      parentName: mixamorig:LeftHandRing3
      position: {x: -0.000020999998, y: 0.024263, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandPinky1
      parentName: mixamorig:LeftHand
      position: {x: -0.034707, y: 0.088622, z: -0.00022500001}
      rotation: {x: 0.04554186, y: 0.08780999, z: -0.05317601, w: 0.9936738}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandPinky2
      parentName: mixamorig:LeftHandPinky1
      position: {x: -0.000139, y: 0.027618999, z: 0}
      rotation: {x: 0.01548402, y: -0.0000655949, z: 0.0060392926, w: 0.9998619}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandPinky3
      parentName: mixamorig:LeftHandPinky2
      position: {x: 0.000068999994, y: 0.022263, z: 0}
      rotation: {x: 0.037141148, y: -0.000107193795, z: 0.0049348413, w: 0.99929786}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandPinky4
      parentName: mixamorig:LeftHandPinky3
      position: {x: 0.00007, y: 0.020018999, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightShoulder
      parentName: mixamorig:Spine2
      position: {x: 0.065841, y: 0.136792, z: -0.002148}
      rotation: {x: 0.28583264, y: 0.28939632, z: -0.7043597, w: 0.5817448}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightArm
      parentName: mixamorig:RightShoulder
      position: {x: -0, y: 0.137822, z: 0}
      rotation: {x: -0.038267583, y: 0.21065922, z: 0.053300697, w: 0.975355}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightForeArm
      parentName: mixamorig:RightArm
      position: {x: 0.00071099994, y: 0.25995702, z: 0.00063699996}
      rotation: {x: -0.0031603598, y: -0.006124896, z: -0.00533481, w: 0.99996203}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHand
      parentName: mixamorig:RightForeArm
      position: {x: 0.00063699996, y: 0.239307, z: 0.000133}
      rotation: {x: -0.116270594, y: -0.3494145, z: -0.27730897, w: 0.8874065}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb1
      parentName: mixamorig:RightHand
      position: {x: -0.029432, y: 0.028685998, z: 0.011499999}
      rotation: {x: 0.24851863, y: 0.09324449, z: 0.21405531, w: 0.94006616}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb2
      parentName: mixamorig:RightHandThumb1
      position: {x: -0.0062080002, y: 0.033572998, z: 0}
      rotation: {x: -0.02223465, y: 0.18609336, z: 0.07477166, w: 0.9794305}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb3
      parentName: mixamorig:RightHandThumb2
      position: {x: 0.0010289999, y: 0.032879, z: 0}
      rotation: {x: -0.0059513184, y: 0.0071115294, z: 0.06431153, w: 0.9978868}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb4
      parentName: mixamorig:RightHandThumb3
      position: {x: 0.0051789996, y: 0.026438998, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex1
      parentName: mixamorig:RightHand
      position: {x: -0.035862, y: 0.095942, z: 0.00115}
      rotation: {x: 0.06410298, y: -0.003170995, z: 0.06444655, w: 0.9958551}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex2
      parentName: mixamorig:RightHandIndex1
      position: {x: 0.000316, y: 0.032655, z: 0}
      rotation: {x: 0.028814744, y: 0.00017781853, z: -0.008211925, w: 0.9995511}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex3
      parentName: mixamorig:RightHandIndex2
      position: {x: -0.000035999998, y: 0.031061, z: 0}
      rotation: {x: 0.03023024, y: -0.0002164582, z: -0.0079207225, w: 0.9995116}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex4
      parentName: mixamorig:RightHandIndex3
      position: {x: -0.00028, y: 0.025141, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle1
      parentName: mixamorig:RightHand
      position: {x: -0.009961, y: 0.100537, z: -0.003481}
      rotation: {x: 0.046374563, y: 0.00035229305, z: 0.04065043, w: 0.9980966}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle2
      parentName: mixamorig:RightHandMiddle1
      position: {x: 0.000182, y: 0.034892, z: 0}
      rotation: {x: 0.0000003129242, y: 0.00009159249, z: -0.0036278274, w: 0.99999344}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle3
      parentName: mixamorig:RightHandMiddle2
      position: {x: -0.000066, y: 0.032369, z: 0}
      rotation: {x: 0.035155933, y: 0.0000552257, z: -0.0045709717, w: 0.99937135}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle4
      parentName: mixamorig:RightHandMiddle3
      position: {x: -0.000114999995, y: 0.027561, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing1
      parentName: mixamorig:RightHand
      position: {x: 0.011426999, y: 0.099931, z: -0.001979}
      rotation: {x: 0.04739184, y: -0.044255145, z: 0.04735334, w: 0.9967714}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing2
      parentName: mixamorig:RightHandRing1
      position: {x: -0.000034, y: 0.028327998, z: 0}
      rotation: {x: 0.00014855331, y: -0.00012498714, z: -0.0020024849, w: 0.999998}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing3
      parentName: mixamorig:RightHandRing2
      position: {x: -0.000143, y: 0.027663, z: 0}
      rotation: {x: 0.021479784, y: -0.00018192515, z: 0.0040305085, w: 0.9997611}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing4
      parentName: mixamorig:RightHandRing3
      position: {x: 0.000177, y: 0.023448, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandPinky1
      parentName: mixamorig:RightHand
      position: {x: 0.034396, y: 0.089515, z: -0.000145}
      rotation: {x: 0.04442022, y: -0.054115307, z: 0.05078883, w: 0.9962524}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandPinky2
      parentName: mixamorig:RightHandPinky1
      position: {x: 0.000125, y: 0.026461998, z: 0}
      rotation: {x: 0.0075350273, y: -0.00013991808, z: -0.0068158098, w: 0.9999484}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandPinky3
      parentName: mixamorig:RightHandPinky2
      position: {x: -0.000166, y: 0.022618, z: 0}
      rotation: {x: 0.01741616, y: 0.00030092176, z: 0.0029026505, w: 0.9998441}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandPinky4
      parentName: mixamorig:RightHandPinky3
      position: {x: 0.000041, y: 0.019545, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftUpLeg
      parentName: mixamorig:Hips
      position: {x: -0.079836994, y: -0.056943, z: -0.0073519996}
      rotation: {x: -0.4018309, y: 0.013183515, z: 0.9063372, w: 0.13004221}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftLeg
      parentName: mixamorig:LeftUpLeg
      position: {x: -0, y: 0.428936, z: 0}
      rotation: {x: -0.18575662, y: 0.0074613187, z: 0.035450634, w: 0.98192775}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftFoot
      parentName: mixamorig:LeftLeg
      position: {x: -0, y: 0.38235298, z: 0}
      rotation: {x: 0.47753924, y: -0.2891284, z: 0.09840033, w: 0.8238194}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftToeBase
      parentName: mixamorig:LeftFoot
      position: {x: -0, y: 0.182175, z: 0}
      rotation: {x: 0.29115665, y: 0.028351616, z: -0.008632669, w: 0.9562163}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftToe_End
      parentName: mixamorig:LeftToeBase
      position: {x: -0, y: 0.070376, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightUpLeg
      parentName: mixamorig:Hips
      position: {x: 0.079836994, y: -0.056943, z: -0.008311}
      rotation: {x: -0.048238575, y: -0.05161171, z: 0.99563456, w: -0.061000556}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightLeg
      parentName: mixamorig:RightUpLeg
      position: {x: -0, y: 0.42844298, z: 0}
      rotation: {x: -0.2751285, y: -0.0061461506, z: -0.037917413, w: 0.9606398}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightFoot
      parentName: mixamorig:RightLeg
      position: {x: -0, y: 0.382794, z: 0}
      rotation: {x: 0.62497205, y: -0.17709062, z: 0.058840465, w: 0.758015}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightToeBase
      parentName: mixamorig:RightFoot
      position: {x: -0, y: 0.182661, z: 0}
      rotation: {x: 0.29039097, y: -0.029968582, z: 0.009088307, w: 0.9563955}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightToe_End
      parentName: mixamorig:RightToeBase
      position: {x: -0, y: 0.070109, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
