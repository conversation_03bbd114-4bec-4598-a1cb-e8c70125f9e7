using UnityEngine;

public class Player : <PERSON>o<PERSON><PERSON><PERSON><PERSON>
{
    [Header("Paramètres de mouvement")]
    public float moveSpeed = 5f;
    public float jumpForce = 8f;
    
    private Rigidbody rb;
    private bool isGrounded = true;
    
    void Start()
    {
        // Récupérer le composant Rigidbody
        rb = GetComponent<Rigidbody>();
        
        // Si pas de Rigidbody, en ajouter un
        if (rb == null)
        {
            rb = gameObject.AddComponent<Rigidbody>();
        }
    }
    
    void Update()
    {
        // Récupérer les inputs du clavier
        float horizontal = Input.GetAxis("Horizontal"); // A/D ou flèches gauche/droite
        float vertical = Input.GetAxis("Vertical");     // W/S ou flèches haut/bas
        
        // Créer le vecteur de mouvement
        Vector3 movement = new Vector3(horizontal, 0f, vertical);
        
        // Normaliser pour éviter un mouvement plus rapide en diagonal
        if (movement.magnitude > 1f)
        {
            movement = movement.normalized;
        }
        
        // Appliquer le mouvement
        Vector3 moveVelocity = movement * moveSpeed;
        rb.linearVelocity = new Vector3(moveVelocity.x, rb.linearVelocity.y, moveVelocity.z);
        
        // Rotation du joueur vers la direction de mouvement
        if (movement != Vector3.zero)
        {
            transform.rotation = Quaternion.LookRotation(movement);
        }
        
        // Saut avec la barre d'espace
        if (Input.GetKeyDown(KeyCode.Space) && isGrounded)
        {
            rb.linearVelocity = new Vector3(rb.linearVelocity.x, jumpForce, rb.linearVelocity.z);
            isGrounded = false;
        }
    }
    
    // Détecter quand le joueur touche le sol
    void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.CompareTag("Ground") || collision.gameObject.name.Contains("Terrain"))
        {
            isGrounded = true;
        }
    }
}
