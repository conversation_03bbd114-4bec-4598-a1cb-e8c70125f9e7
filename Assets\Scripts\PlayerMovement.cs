using UnityEngine;

public class PlayerMovement : MonoBehaviour
{
    [Header("Vitesse")]
    public float walkSpeed = 2.5f;
    public float runSpeed = 5.5f;
    public float rotationSmoothTime = 0.1f;

    [Header("Saut / Gravité")]
    public float jumpHeight = 1.2f;
    public float gravity = -9.81f;
    public Transform groundCheck;     // un empty sous les pieds
    public float groundCheckRadius = 0.25f;
    public LayerMask groundMask;

    [Header("Référence caméra (pour déplacement relatif)")]
    public Transform cam;

    CharacterController controller;
    float turnVelocity;       // pour SmoothDampAngle
    Vector3 velocity;         // pour la gravité
    bool isGrounded;

    void Awake()
    {
        controller = GetComponent<CharacterController>();
        if (cam == null && Camera.main != null) cam = Camera.main.transform;
    }

    void Update()
    {
        // --- Sol / gravité ---
        if (groundCheck != null)
            isGrounded = Physics.CheckSphere(groundCheck.position, groundCheckRadius, groundMask);
        else
            isGrounded = controller.isGrounded;

        if (isGrounded && velocity.y < 0f) velocity.y = -2f; // colle au sol

        // --- Input ---
        float h = Input.GetAxisRaw("Horizontal");  // A/D ou flèches
        float v = Input.GetAxisRaw("Vertical");    // W/S ou flèches
        Vector3 inputDir = new Vector3(h, 0f, v).normalized;

        // Choix marche / course
        bool running = Input.GetKey(KeyCode.LeftShift);
        float targetSpeed = running ? runSpeed : walkSpeed;

        // Déplacement relatif à la caméra
        if (inputDir.magnitude >= 0.1f)
        {
            float targetAngle = Mathf.Atan2(inputDir.x, inputDir.z) * Mathf.Rad2Deg
                                + (cam ? cam.eulerAngles.y : transform.eulerAngles.y);
            float angle = Mathf.SmoothDampAngle(transform.eulerAngles.y, targetAngle, ref turnVelocity, rotationSmoothTime);
            transform.rotation = Quaternion.Euler(0f, angle, 0f);

            Vector3 moveDir = Quaternion.Euler(0f, targetAngle, 0f) * Vector3.forward;
            controller.Move(moveDir.normalized * targetSpeed * Time.deltaTime);
        }

        // Saut
        if (Input.GetButtonDown("Jump") && isGrounded)
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);

        // Gravité
        velocity.y += gravity * Time.deltaTime;
        controller.Move(velocity * Time.deltaTime);
    }

    // (Optionnel) Gizmo pour voir le groundCheck
    void OnDrawGizmosSelected()
    {
        if (groundCheck != null)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(groundCheck.position, groundCheckRadius);
        }
    }
}
