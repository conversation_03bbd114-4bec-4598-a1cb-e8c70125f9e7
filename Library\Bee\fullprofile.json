{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 14952, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 14952, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 14952, "tid": 2073, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 14952, "tid": 2073, "ts": 1755167248390146, "dur": 345, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 14952, "tid": 2073, "ts": 1755167248390515, "dur": 8, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 14952, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 14952, "tid": 1, "ts": 1755167247542537, "dur": 7115, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14952, "tid": 1, "ts": 1755167247549656, "dur": 223916, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14952, "tid": 1, "ts": 1755167247773574, "dur": 76836, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 14952, "tid": 2073, "ts": 1755167248390525, "dur": 15, "ph": "X", "name": "", "args": {}}, {"pid": 14952, "tid": 240518168576, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247542212, "dur": 41734, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247583949, "dur": 805127, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247583975, "dur": 729, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247584711, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247584715, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247584778, "dur": 474, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247585256, "dur": 4865, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247590197, "dur": 208, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247590409, "dur": 481, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247590895, "dur": 14, "ph": "X", "name": "ProcessMessages 19789", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247590915, "dur": 292, "ph": "X", "name": "ReadAsync 19789", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247591210, "dur": 5, "ph": "X", "name": "ProcessMessages 5899", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247591216, "dur": 394, "ph": "X", "name": "ReadAsync 5899", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247591647, "dur": 4, "ph": "X", "name": "ProcessMessages 4754", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247591653, "dur": 376, "ph": "X", "name": "ReadAsync 4754", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247592265, "dur": 31, "ph": "X", "name": "ProcessMessages 7870", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247592299, "dur": 212, "ph": "X", "name": "ReadAsync 7870", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247592522, "dur": 5, "ph": "X", "name": "ProcessMessages 7007", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247592563, "dur": 663, "ph": "X", "name": "ReadAsync 7007", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247593243, "dur": 14, "ph": "X", "name": "ProcessMessages 4040", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247593270, "dur": 285, "ph": "X", "name": "ReadAsync 4040", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247593563, "dur": 8, "ph": "X", "name": "ProcessMessages 9895", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247593584, "dur": 173, "ph": "X", "name": "ReadAsync 9895", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247593771, "dur": 3, "ph": "X", "name": "ProcessMessages 3512", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247593783, "dur": 112, "ph": "X", "name": "ReadAsync 3512", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247593920, "dur": 4, "ph": "X", "name": "ProcessMessages 3818", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247593934, "dur": 112, "ph": "X", "name": "ReadAsync 3818", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247594070, "dur": 2, "ph": "X", "name": "ProcessMessages 1561", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247594081, "dur": 69, "ph": "X", "name": "ReadAsync 1561", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247594193, "dur": 2, "ph": "X", "name": "ProcessMessages 1933", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247594196, "dur": 256, "ph": "X", "name": "ReadAsync 1933", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247594467, "dur": 2, "ph": "X", "name": "ProcessMessages 1442", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247594470, "dur": 275, "ph": "X", "name": "ReadAsync 1442", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247594752, "dur": 3, "ph": "X", "name": "ProcessMessages 3367", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247594762, "dur": 129, "ph": "X", "name": "ReadAsync 3367", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595067, "dur": 5, "ph": "X", "name": "ProcessMessages 5186", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595077, "dur": 92, "ph": "X", "name": "ReadAsync 5186", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595342, "dur": 14, "ph": "X", "name": "ProcessMessages 3255", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595357, "dur": 74, "ph": "X", "name": "ReadAsync 3255", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595461, "dur": 5, "ph": "X", "name": "ProcessMessages 3434", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595470, "dur": 294, "ph": "X", "name": "ReadAsync 3434", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595780, "dur": 1, "ph": "X", "name": "ProcessMessages 2068", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595782, "dur": 37, "ph": "X", "name": "ReadAsync 2068", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595826, "dur": 1, "ph": "X", "name": "ProcessMessages 1257", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595827, "dur": 33, "ph": "X", "name": "ReadAsync 1257", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595865, "dur": 56, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247595935, "dur": 156, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247596098, "dur": 2, "ph": "X", "name": "ProcessMessages 2581", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247596109, "dur": 204, "ph": "X", "name": "ReadAsync 2581", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247596316, "dur": 1, "ph": "X", "name": "ProcessMessages 1447", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247596317, "dur": 146, "ph": "X", "name": "ReadAsync 1447", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247596465, "dur": 2, "ph": "X", "name": "ProcessMessages 3217", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247596677, "dur": 63, "ph": "X", "name": "ReadAsync 3217", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247596882, "dur": 3, "ph": "X", "name": "ProcessMessages 5338", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247596892, "dur": 74, "ph": "X", "name": "ReadAsync 5338", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247596969, "dur": 2, "ph": "X", "name": "ProcessMessages 2828", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247596982, "dur": 57, "ph": "X", "name": "ReadAsync 2828", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597042, "dur": 12, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597056, "dur": 62, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597233, "dur": 1, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597235, "dur": 67, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597308, "dur": 2, "ph": "X", "name": "ProcessMessages 2924", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597311, "dur": 34, "ph": "X", "name": "ReadAsync 2924", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597355, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597356, "dur": 43, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597408, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597410, "dur": 40, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597599, "dur": 1, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597601, "dur": 110, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597713, "dur": 2, "ph": "X", "name": "ProcessMessages 3735", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597716, "dur": 57, "ph": "X", "name": "ReadAsync 3735", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597780, "dur": 1, "ph": "X", "name": "ProcessMessages 1453", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597781, "dur": 27, "ph": "X", "name": "ReadAsync 1453", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597812, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247597814, "dur": 213, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598034, "dur": 2, "ph": "X", "name": "ProcessMessages 3073", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598037, "dur": 65, "ph": "X", "name": "ReadAsync 3073", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598133, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598140, "dur": 57, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598204, "dur": 1, "ph": "X", "name": "ProcessMessages 1661", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598206, "dur": 37, "ph": "X", "name": "ReadAsync 1661", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598245, "dur": 1, "ph": "X", "name": "ProcessMessages 1375", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598247, "dur": 57, "ph": "X", "name": "ReadAsync 1375", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598306, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598307, "dur": 27, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598350, "dur": 190, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598546, "dur": 1, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247598548, "dur": 243, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247599018, "dur": 2, "ph": "X", "name": "ProcessMessages 2117", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247599422, "dur": 314, "ph": "X", "name": "ReadAsync 2117", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247599739, "dur": 10, "ph": "X", "name": "ProcessMessages 14540", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247599757, "dur": 59, "ph": "X", "name": "ReadAsync 14540", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247599822, "dur": 2, "ph": "X", "name": "ProcessMessages 3366", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247599826, "dur": 60, "ph": "X", "name": "ReadAsync 3366", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247599905, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247599907, "dur": 201, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247600122, "dur": 3, "ph": "X", "name": "ProcessMessages 3868", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247600139, "dur": 31, "ph": "X", "name": "ReadAsync 3868", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247600172, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247600174, "dur": 210, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247600387, "dur": 1, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247600389, "dur": 538, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247600971, "dur": 10, "ph": "X", "name": "ProcessMessages 13066", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247600989, "dur": 88, "ph": "X", "name": "ReadAsync 13066", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247601086, "dur": 3, "ph": "X", "name": "ProcessMessages 3552", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247601099, "dur": 189, "ph": "X", "name": "ReadAsync 3552", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247601315, "dur": 2, "ph": "X", "name": "ProcessMessages 1332", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247601329, "dur": 92, "ph": "X", "name": "ReadAsync 1332", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247601423, "dur": 4, "ph": "X", "name": "ProcessMessages 4854", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247601564, "dur": 206, "ph": "X", "name": "ReadAsync 4854", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247601777, "dur": 4, "ph": "X", "name": "ProcessMessages 4268", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247601815, "dur": 95, "ph": "X", "name": "ReadAsync 4268", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602072, "dur": 4, "ph": "X", "name": "ProcessMessages 4810", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602083, "dur": 80, "ph": "X", "name": "ReadAsync 4810", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602166, "dur": 4, "ph": "X", "name": "ProcessMessages 4693", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602178, "dur": 64, "ph": "X", "name": "ReadAsync 4693", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602249, "dur": 1, "ph": "X", "name": "ProcessMessages 1942", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602255, "dur": 43, "ph": "X", "name": "ReadAsync 1942", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602304, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602311, "dur": 152, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602477, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602479, "dur": 196, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602679, "dur": 1, "ph": "X", "name": "ProcessMessages 2216", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602681, "dur": 214, "ph": "X", "name": "ReadAsync 2216", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602901, "dur": 11, "ph": "X", "name": "ProcessMessages 3155", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602913, "dur": 56, "ph": "X", "name": "ReadAsync 3155", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602978, "dur": 3, "ph": "X", "name": "ProcessMessages 3366", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247602988, "dur": 43, "ph": "X", "name": "ReadAsync 3366", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247603038, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247603040, "dur": 185, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247603227, "dur": 6, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247603244, "dur": 70, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247603323, "dur": 3, "ph": "X", "name": "ProcessMessages 3219", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247603478, "dur": 284, "ph": "X", "name": "ReadAsync 3219", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247603913, "dur": 5, "ph": "X", "name": "ProcessMessages 5752", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247603931, "dur": 192, "ph": "X", "name": "ReadAsync 5752", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247604129, "dur": 8, "ph": "X", "name": "ProcessMessages 10305", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247604302, "dur": 411, "ph": "X", "name": "ReadAsync 10305", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247604722, "dur": 8, "ph": "X", "name": "ProcessMessages 10328", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247604736, "dur": 89, "ph": "X", "name": "ReadAsync 10328", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247604831, "dur": 4, "ph": "X", "name": "ProcessMessages 5937", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247604996, "dur": 210, "ph": "X", "name": "ReadAsync 5937", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247605219, "dur": 2, "ph": "X", "name": "ProcessMessages 2706", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247605234, "dur": 73, "ph": "X", "name": "ReadAsync 2706", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247605477, "dur": 16, "ph": "X", "name": "ProcessMessages 3805", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247605502, "dur": 75, "ph": "X", "name": "ReadAsync 3805", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247605587, "dur": 3, "ph": "X", "name": "ProcessMessages 4101", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247605591, "dur": 63, "ph": "X", "name": "ReadAsync 4101", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247606078, "dur": 2, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247606087, "dur": 206, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247606444, "dur": 21, "ph": "X", "name": "ProcessMessages 10171", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247606475, "dur": 45, "ph": "X", "name": "ReadAsync 10171", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247606533, "dur": 24, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247607108, "dur": 119, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247607239, "dur": 7, "ph": "X", "name": "ProcessMessages 10769", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247607502, "dur": 202, "ph": "X", "name": "ReadAsync 10769", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247607706, "dur": 1, "ph": "X", "name": "ProcessMessages 2204", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247607721, "dur": 163, "ph": "X", "name": "ReadAsync 2204", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247607887, "dur": 25, "ph": "X", "name": "ProcessMessages 2020", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247607926, "dur": 72, "ph": "X", "name": "ReadAsync 2020", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247608012, "dur": 3, "ph": "X", "name": "ProcessMessages 2128", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247608016, "dur": 98, "ph": "X", "name": "ReadAsync 2128", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247608162, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247608164, "dur": 30, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247608259, "dur": 2, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247608268, "dur": 448, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247608772, "dur": 3, "ph": "X", "name": "ProcessMessages 2479", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247608788, "dur": 255, "ph": "X", "name": "ReadAsync 2479", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247609050, "dur": 3, "ph": "X", "name": "ProcessMessages 2463", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247609202, "dur": 300, "ph": "X", "name": "ReadAsync 2463", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247609620, "dur": 27, "ph": "X", "name": "ProcessMessages 3238", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247609649, "dur": 118, "ph": "X", "name": "ReadAsync 3238", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247609771, "dur": 14, "ph": "X", "name": "ProcessMessages 4192", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247609791, "dur": 246, "ph": "X", "name": "ReadAsync 4192", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610048, "dur": 1, "ph": "X", "name": "ProcessMessages 1645", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610050, "dur": 57, "ph": "X", "name": "ReadAsync 1645", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610128, "dur": 1, "ph": "X", "name": "ProcessMessages 2248", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610131, "dur": 43, "ph": "X", "name": "ReadAsync 2248", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610199, "dur": 26, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610251, "dur": 98, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610356, "dur": 2, "ph": "X", "name": "ProcessMessages 2296", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610360, "dur": 91, "ph": "X", "name": "ReadAsync 2296", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610464, "dur": 2, "ph": "X", "name": "ProcessMessages 980", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610467, "dur": 41, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610545, "dur": 2, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610568, "dur": 201, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610780, "dur": 1, "ph": "X", "name": "ProcessMessages 1511", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610782, "dur": 71, "ph": "X", "name": "ReadAsync 1511", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610861, "dur": 3, "ph": "X", "name": "ProcessMessages 2818", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610869, "dur": 59, "ph": "X", "name": "ReadAsync 2818", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610935, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610937, "dur": 36, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247610991, "dur": 59, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247611075, "dur": 147, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247611244, "dur": 84, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247611339, "dur": 3, "ph": "X", "name": "ProcessMessages 4349", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247611348, "dur": 51, "ph": "X", "name": "ReadAsync 4349", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247611522, "dur": 12, "ph": "X", "name": "ProcessMessages 1299", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247611556, "dur": 474, "ph": "X", "name": "ReadAsync 1299", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612037, "dur": 4, "ph": "X", "name": "ProcessMessages 6470", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612053, "dur": 74, "ph": "X", "name": "ReadAsync 6470", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612131, "dur": 2, "ph": "X", "name": "ProcessMessages 2023", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612179, "dur": 45, "ph": "X", "name": "ReadAsync 2023", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612234, "dur": 1, "ph": "X", "name": "ProcessMessages 1194", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612237, "dur": 35, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612281, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612491, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612493, "dur": 69, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612697, "dur": 15, "ph": "X", "name": "ProcessMessages 2579", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612712, "dur": 62, "ph": "X", "name": "ReadAsync 2579", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612785, "dur": 1, "ph": "X", "name": "ProcessMessages 2380", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612788, "dur": 39, "ph": "X", "name": "ReadAsync 2380", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247612834, "dur": 41, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613000, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613013, "dur": 193, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613211, "dur": 2, "ph": "X", "name": "ProcessMessages 2520", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613220, "dur": 52, "ph": "X", "name": "ReadAsync 2520", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613283, "dur": 1, "ph": "X", "name": "ProcessMessages 1574", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613294, "dur": 21, "ph": "X", "name": "ReadAsync 1574", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613352, "dur": 181, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613544, "dur": 47, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613606, "dur": 3, "ph": "X", "name": "ProcessMessages 2474", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613618, "dur": 203, "ph": "X", "name": "ReadAsync 2474", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613824, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247613836, "dur": 231, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247614069, "dur": 3, "ph": "X", "name": "ProcessMessages 2367", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247614239, "dur": 78, "ph": "X", "name": "ReadAsync 2367", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247614329, "dur": 9, "ph": "X", "name": "ProcessMessages 3835", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247614342, "dur": 45, "ph": "X", "name": "ReadAsync 3835", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247614393, "dur": 1, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247614405, "dur": 177, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247614704, "dur": 2, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247614719, "dur": 85, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247614812, "dur": 3, "ph": "X", "name": "ProcessMessages 3547", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247614957, "dur": 90, "ph": "X", "name": "ReadAsync 3547", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615056, "dur": 2, "ph": "X", "name": "ProcessMessages 2335", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615058, "dur": 68, "ph": "X", "name": "ReadAsync 2335", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615138, "dur": 2, "ph": "X", "name": "ProcessMessages 1249", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615293, "dur": 58, "ph": "X", "name": "ReadAsync 1249", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615355, "dur": 2, "ph": "X", "name": "ProcessMessages 2522", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615359, "dur": 52, "ph": "X", "name": "ReadAsync 2522", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615412, "dur": 149, "ph": "X", "name": "ProcessMessages 1300", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615563, "dur": 76, "ph": "X", "name": "ReadAsync 1300", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615651, "dur": 9, "ph": "X", "name": "ProcessMessages 2382", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615660, "dur": 30, "ph": "X", "name": "ReadAsync 2382", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615729, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247615741, "dur": 319, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247616315, "dur": 18, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247616335, "dur": 222, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247616570, "dur": 6, "ph": "X", "name": "ProcessMessages 6897", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247616581, "dur": 186, "ph": "X", "name": "ReadAsync 6897", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247616771, "dur": 3, "ph": "X", "name": "ProcessMessages 2377", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247616936, "dur": 328, "ph": "X", "name": "ReadAsync 2377", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247617272, "dur": 167, "ph": "X", "name": "ProcessMessages 3612", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247617451, "dur": 243, "ph": "X", "name": "ReadAsync 3612", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247617700, "dur": 14, "ph": "X", "name": "ProcessMessages 5730", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247617722, "dur": 47, "ph": "X", "name": "ReadAsync 5730", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247617778, "dur": 1, "ph": "X", "name": "ProcessMessages 2319", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247617781, "dur": 32, "ph": "X", "name": "ReadAsync 2319", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247617816, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247617850, "dur": 173, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247618033, "dur": 48, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247618089, "dur": 2, "ph": "X", "name": "ProcessMessages 3125", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247618092, "dur": 21, "ph": "X", "name": "ReadAsync 3125", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247618123, "dur": 338, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247618474, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247618556, "dur": 4, "ph": "X", "name": "ProcessMessages 5294", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247618647, "dur": 49, "ph": "X", "name": "ReadAsync 5294", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247618702, "dur": 1, "ph": "X", "name": "ProcessMessages 1929", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247618704, "dur": 50, "ph": "X", "name": "ReadAsync 1929", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247618757, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247619556, "dur": 260, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247619846, "dur": 183, "ph": "X", "name": "ProcessMessages 10553", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247620037, "dur": 67, "ph": "X", "name": "ReadAsync 10553", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247620113, "dur": 18, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247620137, "dur": 285, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247620438, "dur": 286, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247620741, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247620744, "dur": 77, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247620863, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247620871, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247620980, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247620982, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247621137, "dur": 37, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247621188, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247621279, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247621283, "dur": 1830, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247623168, "dur": 14, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247623184, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247623273, "dur": 2, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247623276, "dur": 60, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247623420, "dur": 50, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247623484, "dur": 682, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247624207, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247624240, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247624692, "dur": 4, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247624955, "dur": 361, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247625340, "dur": 4, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247625402, "dur": 42, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247625473, "dur": 3, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247625530, "dur": 365, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247626004, "dur": 9, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247626015, "dur": 80, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247626098, "dur": 4, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247626123, "dur": 368, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247626495, "dur": 3, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247626540, "dur": 73, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247626630, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247626648, "dur": 148, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247626870, "dur": 13, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247626885, "dur": 136, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627023, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627291, "dur": 160, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627508, "dur": 6, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627525, "dur": 73, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627612, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627622, "dur": 231, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627861, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627863, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627914, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627917, "dur": 30, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247627963, "dur": 5374, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247633384, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247633469, "dur": 594, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247634268, "dur": 256, "ph": "X", "name": "ProcessMessages 5728", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247634554, "dur": 13524, "ph": "X", "name": "ReadAsync 5728", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247648488, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247648494, "dur": 472, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247649190, "dur": 77, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247649365, "dur": 9338, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247658726, "dur": 81, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247658813, "dur": 258, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247659075, "dur": 63, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247659141, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247659444, "dur": 48, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247659495, "dur": 277, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247660049, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247660052, "dur": 40523, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247700585, "dur": 42, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247700678, "dur": 682, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247701514, "dur": 404, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247701922, "dur": 489, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247702606, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247702676, "dur": 441, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247703266, "dur": 73, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247703551, "dur": 308, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247703904, "dur": 51, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247704047, "dur": 192, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247704367, "dur": 180, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247704648, "dur": 92, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247704743, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247704904, "dur": 204, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247705187, "dur": 57, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247705430, "dur": 204, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247705688, "dur": 36, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247705917, "dur": 112, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247706130, "dur": 50, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247706183, "dur": 323, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247706620, "dur": 67, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247706767, "dur": 781, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247707552, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247707639, "dur": 507, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247708347, "dur": 92, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247708522, "dur": 424, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247709164, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247709168, "dur": 54, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247709725, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247709730, "dur": 769, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247710803, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247710809, "dur": 1058, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247711915, "dur": 68, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247711986, "dur": 406, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247712434, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247712473, "dur": 144, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247712620, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247712623, "dur": 389, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247713247, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247713256, "dur": 386, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247713708, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247713909, "dur": 173, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247714296, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247714300, "dur": 30410, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247744727, "dur": 16, "ph": "X", "name": "ProcessMessages 1316", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247744745, "dur": 66999, "ph": "X", "name": "ReadAsync 1316", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247811756, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247811761, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247811798, "dur": 209, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247812011, "dur": 9292, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821311, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821314, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821517, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821521, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821569, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821571, "dur": 289, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821864, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821866, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821892, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821894, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821938, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821972, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247821974, "dur": 529, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247822662, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247822665, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247822708, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247822711, "dur": 903, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247823619, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247823691, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247823695, "dur": 180, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247823878, "dur": 12, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247823892, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247823929, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247823932, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247824017, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247824019, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247824052, "dur": 318, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247824373, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247824375, "dur": 1130, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247825915, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247825919, "dur": 67, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247825990, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247825992, "dur": 794, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247826791, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247826796, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247826843, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247826845, "dur": 504, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247827353, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247827357, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247827397, "dur": 380, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247828101, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247828128, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247828167, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247828170, "dur": 640, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247828828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247828830, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247828879, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247828881, "dur": 405, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247829317, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247829320, "dur": 332, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247829656, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247829658, "dur": 364, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247830333, "dur": 11, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247830345, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247830532, "dur": 13, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247830547, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247830590, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247830592, "dur": 337, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247830934, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831026, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831029, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831071, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831074, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831116, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831118, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831173, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831202, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831204, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831272, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831274, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831310, "dur": 363, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831678, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831680, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831709, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831711, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831810, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831812, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831848, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247831851, "dur": 239, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247832095, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247832097, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247832128, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247832130, "dur": 652, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247832786, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247832788, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247832834, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247832836, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247833079, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247833124, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247833127, "dur": 327, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247833458, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247833460, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247833484, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247833533, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247833539, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247833571, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247833573, "dur": 662, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247834239, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247834273, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247834274, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247834520, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247834551, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247834848, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247834850, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247834890, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247834891, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247835077, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247835104, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247835107, "dur": 443, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247835555, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247835587, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247835589, "dur": 197, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247835791, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247835823, "dur": 537, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836365, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836392, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836470, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836498, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836500, "dur": 290, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836795, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836824, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836825, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836914, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836940, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247836942, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247837018, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247837046, "dur": 430, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247837480, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247837527, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247837529, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247837706, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247837736, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247837738, "dur": 497, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247838239, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247838267, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247838269, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247838361, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247838389, "dur": 307, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247838699, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247838748, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247838750, "dur": 300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839054, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839084, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839086, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839155, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839181, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839183, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839210, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839213, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839242, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839425, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839452, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839454, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839638, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839666, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247839667, "dur": 812, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247840484, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247840512, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247840514, "dur": 229, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247840747, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247840749, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247840784, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247840788, "dur": 405, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247841197, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247841228, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247841229, "dur": 256, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247841490, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247841529, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247841531, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247841617, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247841654, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247841657, "dur": 447, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247842107, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247842109, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247842186, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247842188, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247842375, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247842410, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247842413, "dur": 394, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247842812, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247842844, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247842847, "dur": 343, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843192, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843194, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843229, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843232, "dur": 368, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843604, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843638, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843640, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843693, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843695, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843725, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843831, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843857, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247843974, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247844004, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247844006, "dur": 316, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247844327, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247844357, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247844359, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247844392, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247844421, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247844423, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247844456, "dur": 694, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247845155, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247845187, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247845190, "dur": 681, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247845874, "dur": 11, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247845887, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247845911, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846019, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846048, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846050, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846079, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846163, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846191, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846193, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846220, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846221, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846247, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846329, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846361, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846363, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846390, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846392, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846418, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846447, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846451, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846491, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846494, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846523, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846524, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846559, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846582, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846609, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846636, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846638, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846681, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846684, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846717, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846718, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846749, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846922, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846924, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846963, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247846967, "dur": 32, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847002, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847004, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847034, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847036, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847066, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847068, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847120, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847122, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847161, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847165, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847196, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847198, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847234, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847236, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847282, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847285, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847315, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847318, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847346, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847347, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847377, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847379, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847427, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847430, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847464, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847467, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847487, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847489, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847515, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847517, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847548, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847551, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847584, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847586, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847617, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847620, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847670, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847673, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847704, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847707, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847737, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847739, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847769, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847771, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847806, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847808, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847842, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847844, "dur": 29, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847876, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847878, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847904, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847906, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847936, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847938, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847983, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247847987, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848024, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848026, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848057, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848059, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848089, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848091, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848123, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848125, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848156, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848159, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848194, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848197, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848228, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848231, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848262, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848264, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848321, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848323, "dur": 29, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848355, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848357, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848403, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848406, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848453, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848456, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848493, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848495, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848542, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848544, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848579, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848611, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167247848637, "dur": 166303, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248014950, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248014955, "dur": 143, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248015101, "dur": 24, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248015126, "dur": 32080, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248047218, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248047222, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248047254, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248047257, "dur": 97188, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248144473, "dur": 14, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248144495, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248144582, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248144590, "dur": 133237, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248277843, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248277848, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248277907, "dur": 30, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248277938, "dur": 13047, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248290993, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248290996, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248291047, "dur": 24, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248291073, "dur": 6326, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248297407, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248297410, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248297436, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248297439, "dur": 1714, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248299160, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248299165, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248299213, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248299234, "dur": 70583, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248369827, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248369831, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248369862, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248369866, "dur": 1566, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248371440, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248371442, "dur": 104, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248371551, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248371577, "dur": 1449, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248373032, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248373072, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 14952, "tid": 240518168576, "ts": 1755167248373074, "dur": 15988, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 14952, "tid": 2073, "ts": 1755167248390543, "dur": 4883, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 14952, "tid": 236223201280, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 14952, "tid": 236223201280, "ts": 1755167247540606, "dur": 309823, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 14952, "tid": 236223201280, "ts": 1755167247850430, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 14952, "tid": 236223201280, "ts": 1755167247850432, "dur": 55, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 14952, "tid": 2073, "ts": 1755167248395430, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 14952, "tid": 231928233984, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 14952, "tid": 231928233984, "ts": 1755167247523680, "dur": 865438, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 14952, "tid": 231928233984, "ts": 1755167247527134, "dur": 13362, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 14952, "tid": 231928233984, "ts": 1755167248389124, "dur": 64, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 14952, "tid": 231928233984, "ts": 1755167248389137, "dur": 32, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 14952, "tid": 231928233984, "ts": 1755167248389190, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 14952, "tid": 2073, "ts": 1755167248395442, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1755167247585415, "dur": 2984, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755167247588409, "dur": 1675, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755167247590232, "dur": 156, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1755167247590389, "dur": 396, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755167247591946, "dur": 190, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_D8824977E7F2B23E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247592470, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9AB55CF056145574.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247592748, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_A7499BA3B50749DD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247593194, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247593877, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1755167247594321, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1755167247594824, "dur": 139, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1755167247595123, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1755167247595378, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1755167247596026, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.SecurityCore.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1755167247596332, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247596617, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1755167247598212, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Threading.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247600962, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1755167247601288, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247601578, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247602245, "dur": 148, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1755167247602863, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1755167247603114, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.Stores.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247603347, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247603621, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1755167247605029, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247605470, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1755167247605995, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VSCode.Editor.ref.dll_3628369EB48E4C19.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755167247606284, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1755167247607045, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1755167247607693, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1755167247608690, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1755167247614563, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1755167247619005, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755167247590812, "dur": 30180, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755167247621012, "dur": 752017, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755167248373036, "dur": 83, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755167248374366, "dur": 77, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755167248374469, "dur": 1758, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1755167247590916, "dur": 30106, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247621041, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247621304, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_7FE145A456DFCBEF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247621605, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247622140, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F016717A46FE59BB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247622209, "dur": 2282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247624551, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247624774, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247625056, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247625245, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247625481, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247625792, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247625901, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247626123, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247626632, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247626830, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247627081, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247627285, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247627483, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247627685, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247627740, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247627843, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247627928, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247628111, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247628314, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.SecurityCore.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1755167247628371, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247628463, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247628539, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247628694, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247629094, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247629258, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247629421, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247629590, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247629870, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247630076, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247630343, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247630531, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247630719, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247630874, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247631206, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247631390, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247631537, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247631605, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247631750, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247631959, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247632232, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247632440, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247632676, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247632814, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247633015, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247633186, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247633268, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247633474, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247633744, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247633971, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247634191, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247634440, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247634722, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247634784, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247635239, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247636342, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247637516, "dur": 1868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247639384, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247640604, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247641051, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247643552, "dur": 797, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@1f81ab32c638\\Editor\\Material\\UIBlocks\\EmissionUIBlock.cs"}}, {"pid": 12345, "tid": 1, "ts": 1755167247642190, "dur": 2737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247644928, "dur": 1789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247646718, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247648362, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247648784, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247649180, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247649693, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247650306, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247652033, "dur": 2876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247654909, "dur": 3038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247657947, "dur": 2694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247660651, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247660870, "dur": 2447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247663318, "dur": 3007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247666326, "dur": 3011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247669338, "dur": 3154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247672493, "dur": 3289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247675783, "dur": 3275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247679058, "dur": 1628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247680687, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247681086, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247681525, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247682174, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247682608, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247683040, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247683517, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247683945, "dur": 1937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247685883, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247686323, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247686747, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247687163, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247688640, "dur": 2109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247690749, "dur": 2264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247693014, "dur": 2210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247695225, "dur": 3117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247698346, "dur": 2822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247701169, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247701989, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247702771, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247702942, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247703006, "dur": 1475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247704482, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247704718, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247704774, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247704932, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247705113, "dur": 2233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247707347, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247707593, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247707791, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247708027, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247708743, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247709189, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247709327, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Registration.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247709495, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247709571, "dur": 1008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Registration.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247710584, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247710828, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247710903, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247711127, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247711331, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TerrainTools.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247711474, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247711530, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247711685, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247711741, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247711885, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247712108, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247712277, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247712349, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ALP8310_ControllerGlobal.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247712796, "dur": 1158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247713959, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247714534, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247714684, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247715579, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247716101, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247716173, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247716411, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247716869, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247717225, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247717281, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755167247717452, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247717596, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247718052, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247718197, "dur": 102547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247820746, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247823120, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247823286, "dur": 2273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247825560, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247825771, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247828059, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247828176, "dur": 2855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247831032, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247831344, "dur": 2075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247833420, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247833591, "dur": 3359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247836951, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247837064, "dur": 2661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Analytics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247839726, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247839874, "dur": 3795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247843671, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247843909, "dur": 1894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247845803, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247845902, "dur": 1676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755167247847579, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247847729, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247847948, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247848222, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247848411, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247848475, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247848559, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247848707, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247848871, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247848930, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247849041, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247849279, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247849406, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247849515, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247849886, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755167247850015, "dur": 522980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247590968, "dur": 30065, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247621046, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247621174, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1755167247621296, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2D8413BDE13CD7F1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247621565, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247622107, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_01F0438719F4637C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247622181, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247622429, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247622550, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247622634, "dur": 1995, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247624668, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247624831, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247625092, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247625256, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8A562CA37CD388A9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247625455, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247625680, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_22355095CF1A36B0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247625766, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247625880, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247626155, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247626344, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247626460, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247626681, "dur": 32828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247659511, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247660088, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247660310, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247660548, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247660610, "dur": 40939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247701550, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247701968, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247702151, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247702277, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247702589, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247702764, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247702943, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247703622, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247704156, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247704310, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247704878, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247705011, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247705616, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247705809, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Purchasing.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247705979, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247706365, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Purchasing.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247706977, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247707099, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.AppleCore.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247707592, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247707807, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247708076, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247708574, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247709147, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247709218, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247709329, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247709468, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247710118, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247710429, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247710623, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247710710, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247710917, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247710995, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247711297, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247711538, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247711672, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247711749, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Analytics.DataPrivacy.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247711886, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247711955, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247712124, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247712198, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247712416, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247712503, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247713014, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247713354, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247713798, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247713949, "dur": 982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247714932, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247715086, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755167247715258, "dur": 715, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247715978, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247716855, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247717185, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247717251, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247717669, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247717826, "dur": 102703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247820531, "dur": 2327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Analytics.DataPrivacy.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247822859, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247822994, "dur": 2094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247825089, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247825290, "dur": 3872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247829163, "dur": 920, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247830094, "dur": 2200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247832295, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247832427, "dur": 2019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Telemetry.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247834447, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247835037, "dur": 5457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Networking.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247840495, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247840737, "dur": 2174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247842911, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247843008, "dur": 6419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755167247849428, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247849558, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247849725, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247849889, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167247850007, "dur": 448687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755167248298749, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1755167248298702, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1755167248298875, "dur": 1783, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1755167248300667, "dur": 72338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247591187, "dur": 29883, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247621078, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_284B36A6DA56D348.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755167247621478, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247621913, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247622226, "dur": 2111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247624377, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247624824, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247625136, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247625425, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247625609, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247625773, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247625941, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247626198, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247626730, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247626927, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247627056, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1755167247627125, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247627214, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247627298, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247627425, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247627506, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247627609, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1755167247627727, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247627826, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247628186, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247628429, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247628501, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247628757, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755167247628824, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247628881, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247628956, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247629130, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247629335, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247629491, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1755167247629547, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247629775, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247629886, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247630131, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247630310, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247630516, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247630595, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247630866, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247631106, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247631171, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247631372, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247631590, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247631809, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247632143, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247632366, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247632449, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247632515, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247632732, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247632903, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247633155, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247633309, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247633602, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247633694, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247633874, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247634230, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247634472, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247634778, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247635212, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247636200, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247637235, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247637631, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247639039, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247640571, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247641433, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247643474, "dur": 877, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@1f81ab32c638\\Editor\\Material\\ShaderGraph\\Legacy\\FabricMasterNode1.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755167247642937, "dur": 3129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247646066, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247647569, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247649044, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247649483, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247649957, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247650384, "dur": 2353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247652738, "dur": 3247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247655986, "dur": 2886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247658872, "dur": 3167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247662039, "dur": 3042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247665081, "dur": 3171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247668253, "dur": 3236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247671489, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247671853, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247672692, "dur": 3376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247676068, "dur": 3554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247679780, "dur": 692, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Invocations\\MemberInvocationInspector.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755167247679622, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247680783, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247681272, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247682308, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247682762, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247683223, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247683673, "dur": 1686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247685359, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247686472, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247686913, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247687348, "dur": 2275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247689624, "dur": 2040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247691665, "dur": 2210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247693876, "dur": 2766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247696646, "dur": 3098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247699780, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247701278, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247701974, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247702770, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755167247702981, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247703059, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247703947, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247704155, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247704625, "dur": 1210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247705836, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247705989, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Scheduler.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755167247706123, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247706213, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755167247706394, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247706483, "dur": 802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247707285, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247707517, "dur": 669, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247708192, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.AppleStub.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755167247708376, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247708436, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755167247708914, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247708974, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247710123, "dur": 1187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247711322, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247711737, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.Stores.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755167247711888, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247712129, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.Stores.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247712949, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247713245, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247713480, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.Codeless.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755167247713638, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247713824, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.Codeless.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247714410, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247714945, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247715027, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755167247715270, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.Purchasing.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247715914, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247716056, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247716173, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247716242, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247716408, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755167247716548, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247716650, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247717346, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247717591, "dur": 105165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247822759, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247825096, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247825273, "dur": 371, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247825647, "dur": 3490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247829138, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247829307, "dur": 6835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247836144, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247836352, "dur": 1938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247838291, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247838532, "dur": 1977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247840509, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247840717, "dur": 2161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247842879, "dur": 1425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247844315, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247846542, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247846680, "dur": 2015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755167247848695, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247848816, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247848980, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1755167247849041, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247849226, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247849440, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247849498, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247849645, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247849719, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247849778, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247849950, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755167247850039, "dur": 522961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247590954, "dur": 30096, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247621061, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247621178, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1755167247621325, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_DD3CBC65A51B3B4A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247621595, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247622172, "dur": 2069, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247624285, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247624740, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247624919, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247625275, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_74816C903F3AF197.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247625369, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247625630, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247625848, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247626205, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247626447, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247626524, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247626738, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247626887, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247626996, "dur": 22279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247649276, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247649515, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247649988, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247650453, "dur": 1985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247652438, "dur": 3200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247655638, "dur": 2900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247658539, "dur": 3077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247661617, "dur": 2711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247664329, "dur": 3034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247667363, "dur": 2860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247670223, "dur": 2915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247673138, "dur": 3165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247676303, "dur": 3353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247679657, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247680159, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247680581, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247682045, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247682799, "dur": 1316, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Utilities\\ScriptReferenceReplacement.cs"}}, {"pid": 12345, "tid": 4, "ts": 1755167247682478, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247684183, "dur": 2327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247686511, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247686925, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247687353, "dur": 1908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247689262, "dur": 2193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247691455, "dur": 2222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247693678, "dur": 2240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247695918, "dur": 2933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247698851, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247700355, "dur": 1606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247701965, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247702104, "dur": 855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247702961, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.WinRTCore.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247703180, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247703260, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.WinRTCore.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247703690, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247703920, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247704026, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.WinRTStub.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247704359, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247704854, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.WinRTStub.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247705615, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247705851, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247706057, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247706818, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247707026, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247707115, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247707298, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247707482, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247708106, "dur": 1068, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247709185, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247709251, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Components.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247709806, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247710636, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247710834, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/ALP8310_ControllerGlobal.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247710966, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247711032, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247711192, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247711484, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247711636, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247712110, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247712274, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247712355, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247712881, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247713095, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247713737, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247714155, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Analytics.DataPrivacy.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247714830, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247715002, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247715801, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247716255, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247716423, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/IAPResolver.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755167247716595, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247716718, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IAPResolver.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247717342, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247717479, "dur": 103039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247820521, "dur": 3367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Components.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247823890, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247824015, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247826574, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247826713, "dur": 3464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247830178, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247830354, "dur": 2016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247832372, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247832782, "dur": 2060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247834843, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247834964, "dur": 2234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247837199, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247837342, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.HighDefinition.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247839606, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247839746, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.Purchasing.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247842109, "dur": 881, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247843000, "dur": 2029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247845030, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247845205, "dur": 2166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755167247847372, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247847538, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247847722, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247848001, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247848154, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247848331, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247848514, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247848669, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247848863, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247849025, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247849170, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247849275, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247849342, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247849586, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247849765, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247849943, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755167247850034, "dur": 522976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247591154, "dur": 29906, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247621076, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247621178, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1755167247621319, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E7120EB995DA210C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247621591, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247622178, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247622399, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247622598, "dur": 1869, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247624506, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247624706, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247624855, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_316ABDB813889A82.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247624908, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247625066, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247625268, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247625566, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247625669, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247625754, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247625917, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247626194, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247626556, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247626683, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247627089, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247627204, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247627314, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247627420, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247627580, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247627672, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247627912, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247628103, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247628398, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247628495, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247628640, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247628697, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247628864, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247628946, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247629122, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247629291, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247629561, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247629778, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247629874, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247630039, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755167247630094, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247630210, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247630385, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247630587, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247630776, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247631105, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247631229, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247631558, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247631728, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247631903, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247631971, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247632175, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247632272, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247632457, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247632528, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247632707, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247632872, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247633094, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247633170, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247633317, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247633480, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247633739, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247633931, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247634187, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247634435, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247634531, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247634804, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247636200, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247637250, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247637671, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247639181, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247640670, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247641076, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247643480, "dur": 869, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@1f81ab32c638\\Editor\\Material\\Water\\ShaderGraph\\Node\\UnpackData_Water.cs"}}, {"pid": 12345, "tid": 5, "ts": 1755167247642066, "dur": 2646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247644712, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247646442, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247647974, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247649420, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247650281, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247650699, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247652027, "dur": 2763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247654791, "dur": 3286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247658077, "dur": 3143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247661221, "dur": 2570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247663792, "dur": 3280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247667073, "dur": 3157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247670231, "dur": 2812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247673044, "dur": 3032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247676076, "dur": 3367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247679444, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247680311, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247681772, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247682249, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247682663, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247683130, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247683632, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247685184, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247687028, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247687510, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247688988, "dur": 2078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247691067, "dur": 2295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247693362, "dur": 2329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247695692, "dur": 3220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247698913, "dur": 2364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247701277, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247701972, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247702773, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247702910, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247703064, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247703699, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247704051, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247704222, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247704317, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247704572, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TerrainTools.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247704753, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247704809, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247704975, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247705039, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247705965, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247706416, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247706539, "dur": 879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Telemetry.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247707423, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247707609, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247707818, "dur": 1534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247709353, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247709489, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247709550, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247709695, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247709946, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247710389, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247710679, "dur": 57, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247710737, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247710810, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247710963, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247711036, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247711488, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247711752, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247711840, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247712028, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247712113, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247712252, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247712318, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247712860, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247712985, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247713388, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247713707, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247714148, "dur": 790, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247714943, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TerrainTools.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247715741, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247716106, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247716178, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247716238, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247716331, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247716390, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755167247716566, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247717038, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247717520, "dur": 103002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247820524, "dur": 2157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247822682, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247823452, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247825841, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247825974, "dur": 2300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Purchasing.Stores.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247828275, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247828422, "dur": 2503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Threading.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247830926, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247831072, "dur": 1975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Device.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247833048, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247833181, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247835627, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247835746, "dur": 2072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Advertisements.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247837819, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247837996, "dur": 2109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247840106, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247840214, "dur": 2775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247842989, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247843152, "dur": 2047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247845200, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247845347, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755167247847474, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247847593, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247847745, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247848008, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247848124, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247848265, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247848438, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247848622, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247848746, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247848824, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247849003, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247849204, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247849408, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247849471, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247849541, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247849625, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247849685, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247849867, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247849976, "dur": 2022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247854972, "dur": 303, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1755167247855275, "dur": 967, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1755167247856243, "dur": 50, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1755167247851998, "dur": 4300, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755167247856299, "dur": 516687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247591220, "dur": 29861, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247621099, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C7ED8541370EC3E2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247621555, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247622215, "dur": 780, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247623032, "dur": 1638, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247624713, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247624909, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247625248, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1F0AB14E18D23E11.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247625456, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247625635, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_AC0F081190478124.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247625695, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247625781, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247625935, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247626224, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247626631, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247626704, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247627020, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247627208, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247627416, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247627595, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755167247627740, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247627818, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247628089, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247628306, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247628361, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247628480, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247628570, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247628677, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247628847, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755167247628914, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247629059, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247629262, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247629336, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247629653, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247629816, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247629918, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247630121, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247630333, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247630455, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247630671, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755167247630722, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247630803, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247631136, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247631232, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247631506, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247631563, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247631808, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247632071, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247632295, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247632539, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247632746, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247632973, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247633161, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247633299, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247633528, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247633687, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247633869, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247634041, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247634202, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247634416, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247634550, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247634825, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247635681, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247636781, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247638136, "dur": 1435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247639571, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247640842, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247641242, "dur": 1703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247643467, "dur": 821, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@1f81ab32c638\\Editor\\Material\\PBRSky\\ShaderGraph\\PBRSkySubTarget.cs"}}, {"pid": 12345, "tid": 6, "ts": 1755167247642946, "dur": 2843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247645789, "dur": 1728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247647517, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247649452, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247649870, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247650299, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247651410, "dur": 1925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247653336, "dur": 2920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247656256, "dur": 3360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247659616, "dur": 2475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247662091, "dur": 2824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247664916, "dur": 2448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247667365, "dur": 3051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247670417, "dur": 2987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247673404, "dur": 3253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247676657, "dur": 3094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247679752, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247680436, "dur": 1755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247682191, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247682610, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247683050, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247683502, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247683962, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247685875, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247686356, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247686773, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247687177, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247688189, "dur": 1869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247690058, "dur": 1973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247692031, "dur": 1987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247694018, "dur": 2666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247696685, "dur": 3232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247699920, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247701098, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247701965, "dur": 1033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247702999, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247703186, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247703261, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247703714, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247704308, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247704552, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247704901, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247705118, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247705633, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247705885, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247706065, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247706164, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247706715, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247706870, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Device.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247707055, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247707118, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Device.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247708011, "dur": 708, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247708729, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247708896, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Components.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247709027, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247709243, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247709373, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Networking.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247709854, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247710621, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Networking.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247711249, "dur": 921, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247712226, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247712421, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247712505, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247713293, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247713709, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247713952, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247714447, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247714639, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247715326, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247715815, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247716059, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247716129, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247716346, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247716567, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247716658, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247716729, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247716794, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247716872, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247716949, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247717067, "dur": 5007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247722077, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755167247722209, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247722274, "dur": 98250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247820537, "dur": 2151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247822689, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247822957, "dur": 2205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TerrainTools.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247825163, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247825309, "dur": 2509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247827819, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247827990, "dur": 2407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Purchasing.WinRTStub.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247830398, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247830582, "dur": 1959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Purchasing.AppleMacosStub.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247832543, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247832686, "dur": 2196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Configuration.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247834883, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247835046, "dur": 1923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247836970, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247837073, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247839114, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247839218, "dur": 2032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247841251, "dur": 998, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247842258, "dur": 2282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247844541, "dur": 931, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247845482, "dur": 2312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TerrainTools.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755167247847795, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247847927, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247848107, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247848187, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247848322, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247848464, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247848558, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247848760, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247848945, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247849228, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247849333, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247849435, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247849508, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247849576, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247849641, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247849792, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247849938, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167247849999, "dur": 446609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755167248296609, "dur": 76397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247591245, "dur": 29853, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247621103, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_94EF8D6143DA44EF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755167247621571, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247622269, "dur": 1639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247623941, "dur": 841, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247624845, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247625190, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247625560, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247625763, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247625941, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247626023, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247626110, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247626340, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247626668, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247626744, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247626870, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247627073, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247627178, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1755167247627256, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247627333, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247627406, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755167247627462, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247627589, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247627714, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755167247627770, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247627832, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247628076, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247628200, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247628324, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247628410, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247628675, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247628776, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247628838, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755167247628950, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247629154, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247629265, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247629351, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247629458, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247629654, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247629954, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247630206, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247630410, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247630468, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247630572, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247630645, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247630723, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247630809, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247630894, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247631238, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247631410, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247631734, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247631960, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247632173, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247632270, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247632425, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247632699, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247632826, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247632988, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247633235, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247633526, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247633827, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247634041, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247634215, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247634285, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247634587, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247634800, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247635329, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247636874, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247637934, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247638835, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247640287, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247643577, "dur": 831, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@1f81ab32c638\\Editor\\RenderPipeline\\Camera\\HDCameraUI.ContextualMenu.cs"}}, {"pid": 12345, "tid": 7, "ts": 1755167247641671, "dur": 2738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247644409, "dur": 1989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247646399, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247647891, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247649650, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247650109, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247650522, "dur": 1991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247652513, "dur": 3071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247655585, "dur": 3201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247660210, "dur": 680, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Nodes\\Math\\Trigonometry\\Arctangent2Node.cs"}}, {"pid": 12345, "tid": 7, "ts": 1755167247658786, "dur": 3114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247661901, "dur": 2632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247664534, "dur": 3474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247668008, "dur": 3163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247671172, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247672732, "dur": 3190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247675923, "dur": 3564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247679487, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247680005, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247680424, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247682149, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247682614, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247683046, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247683531, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247684051, "dur": 2088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247686139, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247686628, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247687047, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247688759, "dur": 2065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247690827, "dur": 2147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247692975, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247694144, "dur": 2893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247697037, "dur": 2927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247700290, "dur": 1677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247701968, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247702768, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755167247702999, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247703062, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247703681, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247703863, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247703926, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755167247704111, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247704233, "dur": 1833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247706067, "dur": 970, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247707042, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247707764, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247708103, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755167247708281, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247708337, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247708887, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247709201, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247709444, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755167247709638, "dur": 2088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247711727, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247712111, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755167247712280, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247712423, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755167247712511, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247712963, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247713266, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247713740, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247714214, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755167247714418, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247714527, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247715160, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247715347, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/ALP8310_ControllerGlobal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755167247715497, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247715580, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ALP8310_ControllerGlobal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247716024, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247716370, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247716563, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247716644, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247716736, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247716922, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247717037, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247717416, "dur": 103110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247820540, "dur": 4775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247825316, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247825546, "dur": 2438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247827985, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247828209, "dur": 3049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Purchasing.SecurityStub.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247831259, "dur": 1325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247832632, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247834859, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247835060, "dur": 2326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Purchasing.Codeless.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247837387, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247837887, "dur": 2927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Environments.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247840815, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247840938, "dur": 1997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Configuration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247842936, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247843129, "dur": 2204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247845335, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247845938, "dur": 2110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755167247848049, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247848173, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247848326, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247848521, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247848702, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247848872, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247849003, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247849084, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247849340, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247849661, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247849829, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247849971, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755167247850122, "dur": 522880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247591316, "dur": 29890, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247621217, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_32939C22C21A9A23.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755167247621493, "dur": 657, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247622157, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_59C0854D1BDEB3BB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755167247622229, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247622666, "dur": 1970, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247624752, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247624941, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247625190, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247625564, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247625872, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247626135, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247626362, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755167247626419, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247626667, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247626857, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247626949, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247627162, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247627329, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247627405, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755167247627479, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247627556, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247627607, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1755167247627701, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247627758, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247627829, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247627980, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247628341, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247628480, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247628576, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247628629, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755167247628682, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247628759, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247628964, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247629260, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247629531, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.AppleStub.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755167247629612, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247629919, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247630086, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247630344, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247630424, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247630580, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247630648, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247630885, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247631206, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247631437, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247631601, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247631716, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247631958, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247632034, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247632135, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247632452, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247632520, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247632826, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247632883, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247633120, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247633296, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247633478, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247633733, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247633997, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247634190, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247634268, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247634479, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247634665, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247634789, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247635274, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247636236, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247637392, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247638765, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247640297, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247641731, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247643605, "dur": 743, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@1f81ab32c638\\Editor\\Material\\Eye\\ShaderGraph\\Node\\ScleraIrisBlend.cs"}}, {"pid": 12345, "tid": 8, "ts": 1755167247643418, "dur": 2766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247646185, "dur": 1785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247647971, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247649593, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247650077, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247650481, "dur": 2489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247652971, "dur": 3112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247656083, "dur": 3206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247659290, "dur": 2866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247662157, "dur": 3080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247665238, "dur": 3274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247668512, "dur": 3146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247671658, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247672068, "dur": 3037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247675105, "dur": 3228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247678334, "dur": 2344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247680679, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247681530, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247682295, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247683353, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247683799, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247685568, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247685965, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247686407, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247686840, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247687261, "dur": 2169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247689430, "dur": 2064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247691495, "dur": 2472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247693967, "dur": 2882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247696852, "dur": 3020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247699873, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247701515, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247701972, "dur": 790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247702763, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755167247702974, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247703212, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247704187, "dur": 542, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247704744, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247704827, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755167247705022, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247705333, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247705946, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247706097, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247706187, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755167247706304, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247706367, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247706880, "dur": 727, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247707625, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1755167247708336, "dur": 207, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247709568, "dur": 103653, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1755167247820538, "dur": 2106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247822645, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247822780, "dur": 2049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247824830, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247824972, "dur": 2377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Environments.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247827350, "dur": 540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247827914, "dur": 2252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/ALP8310_ControllerGlobal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247830167, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247830324, "dur": 2036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Purchasing.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247832361, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247832483, "dur": 1964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247834448, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247834586, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247836781, "dur": 1632, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247838427, "dur": 2028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Advertisements.DevX.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247840456, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247840559, "dur": 2045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247842605, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247842707, "dur": 1839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/IAPResolver.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247844547, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247844702, "dur": 2214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755167247846917, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247847382, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247847828, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247847985, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247848234, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247848398, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247848496, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247848551, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247848613, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247848908, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247849042, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247849137, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247849381, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247849482, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247849579, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247849649, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247849810, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247849986, "dur": 6323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755167247856309, "dur": 516699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247591342, "dur": 29879, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247621227, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_C7086B172735D898.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755167247621549, "dur": 684, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247622268, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247622619, "dur": 1899, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247624554, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247624827, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247625063, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247625285, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247625470, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247625742, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_728920FEC952B3E5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755167247625801, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247625914, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247626136, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247626415, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247626641, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247626829, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247627209, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247627328, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247627651, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247627771, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247628144, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247628211, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247628349, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247628513, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247628691, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247628867, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247629053, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247629214, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247629268, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247629500, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1755167247629564, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247629873, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247630038, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247630240, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247630413, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247630583, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247630796, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247630854, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247631161, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247631286, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247631561, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247631740, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247631967, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247632198, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247632360, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247632552, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247632780, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247632933, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247633245, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247633402, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247633574, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247633647, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247633798, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247633977, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247634224, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247634482, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247634717, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247634808, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247636490, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247637869, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247639276, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247640763, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247641160, "dur": 1925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247643571, "dur": 749, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@1f81ab32c638\\Editor\\Material\\Hair\\ShaderGraph\\CreateHairShaderGraph.cs"}}, {"pid": 12345, "tid": 9, "ts": 1755167247643085, "dur": 2729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247645815, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247647304, "dur": 1886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247649191, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247649865, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247650325, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247652117, "dur": 3235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247655352, "dur": 3103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247660354, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Nodes\\Procedural\\Shape\\RoundedRectangleNode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1755167247658456, "dur": 3180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247661636, "dur": 2847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247664483, "dur": 2797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247667280, "dur": 2920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247670200, "dur": 2837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247673037, "dur": 3070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247676108, "dur": 3285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247679394, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247679804, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247680255, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247681165, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247681824, "dur": 2333, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\And.cs"}}, {"pid": 12345, "tid": 9, "ts": 1755167247681591, "dur": 2716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247684307, "dur": 2513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247686821, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247687482, "dur": 1649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247689132, "dur": 1984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247691116, "dur": 2278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247693395, "dur": 2497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247695892, "dur": 2906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247699820, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Runtime\\CompilerServices\\Aliasing.cs"}}, {"pid": 12345, "tid": 9, "ts": 1755167247698799, "dur": 2428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247701227, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247701979, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247702049, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247703021, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755167247703173, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247703231, "dur": 1236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247704467, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247704826, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247704900, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755167247705221, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247705293, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247705947, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247706530, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Scheduler.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247707285, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247707441, "dur": 512, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247707959, "dur": 3034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247710994, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247711596, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247711708, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247711877, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755167247712473, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247712744, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247713777, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247713949, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247714200, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755167247714392, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247714481, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247715428, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247715952, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247716058, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755167247716215, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247716270, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247716827, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247717140, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755167247717382, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247718020, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247718174, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755167247718252, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247718334, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247718568, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247718669, "dur": 101870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247820539, "dur": 2115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247822655, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247822818, "dur": 2088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Purchasing.WinRTCore.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247824907, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247825056, "dur": 3589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Purchasing.SecurityCore.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247828647, "dur": 799, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247829453, "dur": 2425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Purchasing.AppleCore.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247831880, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247832064, "dur": 2063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Registration.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247834129, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247834289, "dur": 2122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.HighDefinition.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247836412, "dur": 882, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247837303, "dur": 2130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247839434, "dur": 1227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247840670, "dur": 7447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755167247848118, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247848307, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247848366, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247848515, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247848846, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247848904, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247848997, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247849079, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247849206, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247849402, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247849490, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247849662, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247849744, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247849964, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755167247850050, "dur": 522989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247591420, "dur": 29811, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247621238, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2AE61BEFE92D422E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247621524, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247621908, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247622244, "dur": 1304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247623575, "dur": 1053, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247624669, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B3BE1B8334830D52.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247624722, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247624986, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247625257, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247625543, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247625687, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247625884, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247626154, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247626248, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247626540, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7CC9B7F9B9108300.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247626596, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247626809, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247626924, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247627319, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247627512, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247627640, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247627819, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247628079, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247628285, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247628451, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247628700, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247628801, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247629011, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247629201, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247629399, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247629584, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247630081, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247630250, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247630445, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247630669, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247630870, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247630926, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247631123, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247631319, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247631395, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247631451, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247631641, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247631803, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247632138, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247632231, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247632387, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247632446, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247632625, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247632699, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247632786, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247632859, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247632986, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247633217, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247633523, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247633827, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247634061, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247634208, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247634422, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247634547, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247634756, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247634832, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247635839, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247636736, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247637913, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247639197, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247640710, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247641102, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247643497, "dur": 847, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@1f81ab32c638\\Editor\\Material\\StackLit\\ShaderGraph\\StackLitAdvancedOptionsPropertyBlock.cs"}}, {"pid": 12345, "tid": 10, "ts": 1755167247642556, "dur": 2774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247645331, "dur": 1901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247647232, "dur": 2276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247649515, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247649942, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247650376, "dur": 1699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247652076, "dur": 2703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247654779, "dur": 3232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247658011, "dur": 3415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247661426, "dur": 2589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247664016, "dur": 3117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247667133, "dur": 3002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247670135, "dur": 3278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247673414, "dur": 3145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247676559, "dur": 3054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247679614, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247680114, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247681514, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247681966, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247682380, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247682796, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247683263, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247683704, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247685338, "dur": 2199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247687538, "dur": 1719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247689258, "dur": 2168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247693425, "dur": 647, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Devices\\IEventPreProcessor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1755167247691427, "dur": 2645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247694073, "dur": 3103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247697176, "dur": 3099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247700293, "dur": 1675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247701969, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247702779, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.SecurityCore.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247703115, "dur": 1373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247704512, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.SecurityCore.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167247705107, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247705740, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247705889, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247705966, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Telemetry.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247706108, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247706191, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247706343, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247706398, "dur": 1078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167247707476, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247707788, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247708245, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167247708776, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247709359, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247709422, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247709635, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167247710415, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247710756, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247711124, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247711202, "dur": 2620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167247713823, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247714201, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247714320, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247714536, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247714636, "dur": 1848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167247716485, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247716798, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247717080, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247717244, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247717328, "dur": 1133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167247718463, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247718635, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247718691, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247718811, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247718871, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167247719271, "dur": 2729, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247722068, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755167247722240, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167247722903, "dur": 55, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167247723816, "dur": 292555, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167248020866, "dur": 27607, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1755167248020416, "dur": 28164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167248048585, "dur": 79, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755167248048751, "dur": 243652, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1755167248296573, "dur": 76453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247591447, "dur": 29791, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247621242, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_72CE526178B0FB4C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247621520, "dur": 609, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247622135, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_E8798D0BA79A4C08.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247622191, "dur": 1038, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247623257, "dur": 1362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247624651, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247625091, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247625420, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247625686, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247625920, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247626172, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247626422, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Analytics.StandardEvents.dll_95311E6DDF2FBAF5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247626505, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247626679, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247626988, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247627126, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247627292, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247627443, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247627499, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247627715, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247627782, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247628089, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247628185, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247628334, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247628459, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247628582, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247628665, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247628814, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247628903, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247628975, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247629212, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247629424, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247629624, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247629952, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247630202, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247630391, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247630602, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247630803, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247631116, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247631496, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247631731, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247631901, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247632135, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247632296, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247632460, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247632697, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247632820, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247632968, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247633022, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247633120, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247633279, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247633714, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247633866, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247634219, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247634554, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247634792, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247635208, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247636584, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247637666, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247639302, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247640637, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247641034, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247643482, "dur": 861, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@1f81ab32c638\\Editor\\Material\\Unlit\\ShaderGraph\\HDUnlitDistortionPropertyBlock.cs"}}, {"pid": 12345, "tid": 11, "ts": 1755167247642183, "dur": 2602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247644786, "dur": 1647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247646434, "dur": 1579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247648014, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247648864, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247649296, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247649916, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247650472, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247652452, "dur": 2999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247655452, "dur": 2965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247658418, "dur": 2536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247660955, "dur": 2159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247663115, "dur": 3092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247666207, "dur": 3265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247669472, "dur": 3126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247672599, "dur": 3184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247675784, "dur": 3382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247679167, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247680119, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247681400, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247682110, "dur": 735, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Lifecycle\\OnDestroy.cs"}}, {"pid": 12345, "tid": 11, "ts": 1755167247681839, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247682969, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247683447, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247683884, "dur": 1962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247685846, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247686361, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247686945, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247687371, "dur": 1855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247689227, "dur": 2156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247691383, "dur": 2169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247693597, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\bool4x3.gen.cs"}}, {"pid": 12345, "tid": 11, "ts": 1755167247693553, "dur": 3114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247696667, "dur": 2931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247699599, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247700792, "dur": 1174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247701966, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247702785, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.AppleCore.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247702943, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247703013, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247703300, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247703374, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247703933, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247704073, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_D58562F0DF82BCBA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247704199, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247704264, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247704445, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247704496, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247704606, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247705252, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247705511, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247705624, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TerrainTools.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247706399, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247706681, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247706840, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247706902, "dur": 1379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247708282, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247708442, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Purchasing.AppleStub.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247708841, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247709210, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247709272, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247709462, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247709535, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Configuration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247710036, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247710243, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247710351, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247710420, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247710538, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247710592, "dur": 1261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247711853, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247711991, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247712133, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247712189, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247712369, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247712485, "dur": 963, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247713450, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247714070, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247714417, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247715008, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247715202, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247715320, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247715552, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247716121, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247716272, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247716393, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755167247716546, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247716645, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247717138, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247717510, "dur": 103025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247820541, "dur": 2114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/ALP8310_ControllerGlobal.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247822656, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247822812, "dur": 2272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Environments.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247825085, "dur": 837, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247825929, "dur": 2846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247828776, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247828901, "dur": 2704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Advertisements.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247831606, "dur": 1700, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247833321, "dur": 2570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Scheduler.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247835892, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247836033, "dur": 2177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247838211, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247838303, "dur": 2434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Services.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247840738, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247841150, "dur": 2323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247843474, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247843611, "dur": 2098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247845710, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247845842, "dur": 2008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755167247847851, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247847968, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247848134, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247848337, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247848518, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247848592, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247848656, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247848849, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247848994, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247849181, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247849270, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247849335, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247849422, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247849484, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247849611, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247849714, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247849931, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755167247850031, "dur": 522966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247591471, "dur": 29775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247621247, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE693D9B51C79752.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247621520, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247622163, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B76B86F6FFE10D96.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247622245, "dur": 2194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247624459, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247624808, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247625138, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247625414, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247625541, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247625683, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247625892, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247626189, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247626621, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247626872, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247627002, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247627072, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247627211, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247627400, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755167247627483, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247627572, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247627645, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247627759, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247628164, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247628242, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247628358, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247628487, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247628569, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247628682, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247628752, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755167247628817, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247628880, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247628973, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247629276, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247629525, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1755167247629590, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247629773, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247629955, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247630117, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247630296, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247630527, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247630588, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247630816, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247631147, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247631233, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247631454, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247631726, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247631804, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247631985, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247632148, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247632393, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247632461, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247632761, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247632895, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247633085, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247633227, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247633522, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247633788, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247633977, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247634231, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247634651, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247634806, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247635703, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247636965, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247637322, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247637954, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247639206, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247640830, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247641255, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247643565, "dur": 749, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.high-definition@1f81ab32c638\\Editor\\Material\\ShaderGraph\\ShaderGraphVersion.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755167247642811, "dur": 2616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247645427, "dur": 1725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247647152, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247648817, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247649226, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247649717, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247650169, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247650604, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247652093, "dur": 3057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247655150, "dur": 2992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247658142, "dur": 2571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247660721, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247660782, "dur": 2137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247662920, "dur": 3052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247665972, "dur": 3034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247669007, "dur": 3291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247672298, "dur": 3194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247675493, "dur": 3312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247678806, "dur": 1566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247680373, "dur": 1777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247682150, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247682588, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247683012, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247683627, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247685113, "dur": 1971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247687084, "dur": 1665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247688750, "dur": 2056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247690806, "dur": 2112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247692918, "dur": 2240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247695159, "dur": 3174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247698333, "dur": 2771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247701108, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247701982, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247702767, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247702933, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247703023, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247703665, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247703966, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247704359, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247705307, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247705495, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247705780, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247705952, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247706007, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Environments.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247706461, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247706700, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247706901, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Threading.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247707130, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247707200, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Services.Core.Threading.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247707620, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247707813, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247708236, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247708933, "dur": 867, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247709808, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247709869, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247710050, "dur": 1187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247711239, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247711768, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247711879, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247712040, "dur": 708, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247712752, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247713303, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247713770, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247713898, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247714650, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247715103, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247715300, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247715376, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247715472, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247715604, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247716047, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247716236, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247716317, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247716397, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755167247716547, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247716644, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247717260, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247717409, "dur": 103226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247820639, "dur": 2394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.Purchasing.AppleStub.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247823034, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247823188, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247825409, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247825679, "dur": 2871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Purchasing.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247828551, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247828982, "dur": 6180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247835163, "dur": 1417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247836589, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247838850, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247838992, "dur": 2760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247841753, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247841989, "dur": 2997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247844988, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247845119, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755167247847619, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247847888, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247847951, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247848270, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247848502, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247848584, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247848753, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247848942, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247849000, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247849088, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247849139, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1755167247849200, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247849278, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247849338, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247849496, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247849710, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247849892, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167247849994, "dur": 170428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167248020464, "dur": 121088, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1755167248020424, "dur": 123440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755167248145360, "dur": 420, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755167248147820, "dur": 131461, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755167248298688, "dur": 72568, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1755167248298671, "dur": 72587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1755167248371293, "dur": 1646, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1755167248386513, "dur": 3562, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 14952, "tid": 2073, "ts": 1755167248395693, "dur": 202, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 14952, "tid": 2073, "ts": 1755167248395976, "dur": 5408, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 14952, "tid": 2073, "ts": 1755167248390495, "dur": 10931, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}