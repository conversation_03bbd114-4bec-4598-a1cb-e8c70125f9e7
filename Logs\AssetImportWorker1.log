Using pre-set license
Built from '6000.0/respin/6000.0.51f1-a206c6c19c75' branch; Version is '6000.0.51f1 (01c3ff5872c5) revision 115711'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'fr' Physical Memory: 7877 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-14T09:01:53Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.51f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Users/<USER>/Desktop/Practice
-logFile
Logs/AssetImportWorker1.log
-srvPort
50240
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/Desktop/Practice
C:/Users/<USER>/Desktop/Practice
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [468]  Target information:

Player connection [468]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 937752555 [EditorId] 937752555 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-P1RI1CO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [468] Host joined multi-casting on [***********:54997]...
Player connection [468] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 18.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.51f1 (01c3ff5872c5)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Desktop/Practice/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon RX 640 (ID=0x6987)
    Vendor:   ATI
    VRAM:     4040 MB
    Driver:   31.0.21923.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56728
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.006444 seconds.
- Loaded All Assemblies, in  0.496 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 316 ms
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.698 seconds
Domain Reload Profiling: 1192ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (196ms)
		LoadAssemblies (182ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (187ms)
				TypeCache.ScanAssembly (171ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (698ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (665ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (415ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (61ms)
			ProcessInitializeOnLoadAttributes (126ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.264 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 16.41 ms, found 3 plugins.
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.219 seconds
Domain Reload Profiling: 2479ms
	BeginReloadAssembly (201ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (971ms)
		LoadAssemblies (660ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (434ms)
			TypeCache.Refresh (322ms)
				TypeCache.ScanAssembly (289ms)
			BuildScriptInfoCaches (97ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1219ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (948ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (156ms)
			ProcessInitializeOnLoadAttributes (556ms)
			ProcessInitializeOnLoadMethodAttributes (213ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 4.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 558 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9073 unused Assets / (30.2 MB). Loaded Objects now: 9753.
Memory consumption went from 302.4 MB to 272.2 MB.
Total: 28.922200 ms (FindLiveObjects: 2.273300 ms CreateObjectMapping: 0.849800 ms MarkObjects: 12.626600 ms  DeleteObjects: 13.167800 ms)

========================================================================
Received Import Request.
  Time since last request: 1234.914130 seconds.
  path: Assets/Flooded_Grounds/Scripts/FPSController
  artifactKey: Guid(2ff53f2206196744ca508c54247845cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Flooded_Grounds/Scripts/FPSController using Guid(2ff53f2206196744ca508c54247845cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e15088fd8389a471be3fe82a530a735e') in 0.0176879 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 7.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9035 unused Assets / (27.7 MB). Loaded Objects now: 9755.
Memory consumption went from 231.0 MB to 203.3 MB.
Total: 98.522000 ms (FindLiveObjects: 2.893700 ms CreateObjectMapping: 0.659600 ms MarkObjects: 85.884500 ms  DeleteObjects: 9.081500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 5.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9034 unused Assets / (28.8 MB). Loaded Objects now: 9754.
Memory consumption went from 231.0 MB to 202.2 MB.
Total: 23.841500 ms (FindLiveObjects: 1.528300 ms CreateObjectMapping: 1.365900 ms MarkObjects: 11.659200 ms  DeleteObjects: 9.286300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 5.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9035 unused Assets / (29.3 MB). Loaded Objects now: 9755.
Memory consumption went from 231.0 MB to 201.7 MB.
Total: 57.120200 ms (FindLiveObjects: 2.984700 ms CreateObjectMapping: 1.158400 ms MarkObjects: 40.864400 ms  DeleteObjects: 12.108600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 3.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9035 unused Assets / (29.0 MB). Loaded Objects now: 9755.
Memory consumption went from 231.0 MB to 202.0 MB.
Total: 16.818800 ms (FindLiveObjects: 0.841900 ms CreateObjectMapping: 0.627700 ms MarkObjects: 8.056600 ms  DeleteObjects: 7.291000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 7.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9035 unused Assets / (29.6 MB). Loaded Objects now: 9755.
Memory consumption went from 231.0 MB to 201.5 MB.
Total: 64.177900 ms (FindLiveObjects: 2.818500 ms CreateObjectMapping: 1.078700 ms MarkObjects: 49.770900 ms  DeleteObjects: 10.506200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3547.474763 seconds.
  path: Assets/Scripts/PlayerMovement.cs
  artifactKey: Guid(3f9394bf08e6b3e44b61e0b152b8fe3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/PlayerMovement.cs using Guid(3f9394bf08e6b3e44b61e0b152b8fe3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c93c7276b862443a0d64681860d998b6') in 0.0082156 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 4.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9036 unused Assets / (27.6 MB). Loaded Objects now: 9756.
Memory consumption went from 231.0 MB to 203.4 MB.
Total: 20.636800 ms (FindLiveObjects: 0.808700 ms CreateObjectMapping: 0.877600 ms MarkObjects: 10.436500 ms  DeleteObjects: 8.512200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 3.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9035 unused Assets / (29.3 MB). Loaded Objects now: 9755.
Memory consumption went from 231.0 MB to 201.7 MB.
Total: 18.310400 ms (FindLiveObjects: 0.705400 ms CreateObjectMapping: 0.709600 ms MarkObjects: 9.439400 ms  DeleteObjects: 7.454600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 3.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9034 unused Assets / (29.1 MB). Loaded Objects now: 9754.
Memory consumption went from 231.0 MB to 202.0 MB.
Total: 17.680300 ms (FindLiveObjects: 0.948400 ms CreateObjectMapping: 0.847400 ms MarkObjects: 8.947500 ms  DeleteObjects: 6.935400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 3.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9035 unused Assets / (29.0 MB). Loaded Objects now: 9755.
Memory consumption went from 231.0 MB to 202.0 MB.
Total: 30.630700 ms (FindLiveObjects: 1.015300 ms CreateObjectMapping: 0.787200 ms MarkObjects: 15.350700 ms  DeleteObjects: 13.474500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1360.265355 seconds.
  path: Assets/Flooded_Grounds/PostProcessing/Editor/PropertyDrawers/PlayerMovement.cs
  artifactKey: Guid(b31e8c3e95b9c2541b9cfbcb3a6313f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Flooded_Grounds/PostProcessing/Editor/PropertyDrawers/PlayerMovement.cs using Guid(b31e8c3e95b9c2541b9cfbcb3a6313f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f4eb33b398ee15b5f5b0a45da41b2fa') in 0.0020318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'HDRP/TerrainLit' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/Builtin_TerrainVisualization' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 6.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 9036 unused Assets / (26.1 MB). Loaded Objects now: 9756.
Memory consumption went from 231.1 MB to 205.0 MB.
Total: 63.846600 ms (FindLiveObjects: 2.751700 ms CreateObjectMapping: 1.032500 ms MarkObjects: 51.330800 ms  DeleteObjects: 8.728700 ms)

Prepare: number of updated asset objects reloaded= 0
